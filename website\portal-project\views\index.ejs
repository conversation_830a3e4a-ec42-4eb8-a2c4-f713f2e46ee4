<!-- Hero Section with Animated Elements -->
<section class="hero-section">
  <div class="container text-center">
    <img src="/images/university_logo.png" alt="University of Layyah Logo" height="150" class="mb-4 animate__animated animate__zoomIn">
    <h1 class="display-3 fw-bold animate__animated animate__fadeInDown">University of Layyah Portal</h1>
    <p class="lead fs-4 animate__animated animate__fadeInUp animate__delay-1s">Download our project and provide your valuable feedback</p>
    <div class="mt-4 animate__animated animate__fadeInUp animate__delay-2s">
      <a href="#download" class="btn btn-light btn-lg rounded-pill px-4 py-3 shadow-sm">
        <i class="bi bi-arrow-down-circle me-2"></i>Get Started
      </a>
    </div>
  </div>
</section>

<!-- Main Content Section -->
<div class="container py-5">
  <!-- Download Section -->
  <section id="download" class="download-section text-center p-5 rounded shadow mb-5">
    <div class="row">
      <div class="col-lg-8 mx-auto">
        <h2 class="mb-4 fw-bold"><i class="bi bi-cloud-download text-primary me-2"></i>Download Project</h2>
        <p class="lead mb-4">Click the button below to download the complete project package</p>

        <div class="download-button-wrapper position-relative d-inline-block mb-4">
          <a href="/download/portal.zip" class="btn btn-primary btn-lg px-5 py-3 rounded-pill shadow">
            <i class="bi bi-download me-2"></i>Download Project (ZIP)
          </a>
          <div class="position-absolute top-0 start-100 translate-middle badge rounded-pill bg-danger animate__animated animate__pulse animate__infinite">
            Free
          </div>
        </div>

        <!-- Direct download link as fallback -->
        <div class="mt-2">
          <small class="text-muted">
            <i class="bi bi-info-circle me-1"></i> If download doesn't start, <a href="/download/portal.zip" class="text-primary">click here for direct download</a>
          </small>
        </div>

        <div class="row mt-5 text-start">
          <div class="col-md-4">
            <div class="d-flex align-items-center mb-3">
              <div class="bg-light p-3 rounded-circle me-3">
                <i class="bi bi-file-earmark-zip text-primary fs-4"></i>
              </div>
              <div>
                <h6 class="mb-0 fw-bold">File Size</h6>
                <p class="mb-0 text-muted">38.0 MB</p>
              </div>
            </div>
          </div>
          <div class="col-md-4">
            <div class="d-flex align-items-center mb-3">
              <div class="bg-light p-3 rounded-circle me-3">
                <i class="bi bi-code-slash text-primary fs-4"></i>
              </div>
              <div>
                <h6 class="mb-0 fw-bold">Version</h6>
                <p class="mb-0 text-muted">1.0.0</p>
              </div>
            </div>
          </div>
          <div class="col-md-4">
            <div class="d-flex align-items-center mb-3">
              <div class="bg-light p-3 rounded-circle me-3">
                <i class="bi bi-calendar-check text-primary fs-4"></i>
              </div>
              <div>
                <h6 class="mb-0 fw-bold">Last Updated</h6>
                <p class="mb-0 text-muted">June 15, 2023</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </section>

  <!-- Features Section -->
  <section class="features-section py-5">
    <div class="text-center mb-5">
      <h2 class="fw-bold">Project Features</h2>
      <p class="lead text-muted">Discover what makes our portal special</p>
    </div>

    <div class="row g-4">
      <div class="col-lg-3 col-md-6">
        <div class="card h-100 border-0 shadow-sm">
          <div class="card-body text-center p-4">
            <div class="feature-icon bg-primary bg-gradient text-white rounded-circle mb-4 mx-auto" style="width: 60px; height: 60px; display: flex; align-items: center; justify-content: center;">
              <i class="bi bi-laptop fs-4"></i>
            </div>
            <h4 class="card-title">Responsive Design</h4>
            <p class="card-text text-muted">Works perfectly on all devices - desktop, tablet, and mobile.</p>
          </div>
        </div>
      </div>

      <div class="col-lg-3 col-md-6">
        <div class="card h-100 border-0 shadow-sm">
          <div class="card-body text-center p-4">
            <div class="feature-icon bg-primary bg-gradient text-white rounded-circle mb-4 mx-auto" style="width: 60px; height: 60px; display: flex; align-items: center; justify-content: center;">
              <i class="bi bi-shield-check fs-4"></i>
            </div>
            <h4 class="card-title">Secure System</h4>
            <p class="card-text text-muted">Built with security best practices to protect user data.</p>
          </div>
        </div>
      </div>

      <div class="col-lg-3 col-md-6">
        <div class="card h-100 border-0 shadow-sm">
          <div class="card-body text-center p-4">
            <div class="feature-icon bg-primary bg-gradient text-white rounded-circle mb-4 mx-auto" style="width: 60px; height: 60px; display: flex; align-items: center; justify-content: center;">
              <i class="bi bi-speedometer2 fs-4"></i>
            </div>
            <h4 class="card-title">Fast Performance</h4>
            <p class="card-text text-muted">Optimized for speed to provide the best user experience.</p>
          </div>
        </div>
      </div>

      <div class="col-lg-3 col-md-6">
        <div class="card h-100 border-0 shadow-sm">
          <div class="card-body text-center p-4">
            <div class="feature-icon bg-primary bg-gradient text-white rounded-circle mb-4 mx-auto" style="width: 60px; height: 60px; display: flex; align-items: center; justify-content: center;">
              <i class="bi bi-gear fs-4"></i>
            </div>
            <h4 class="card-title">Easy to Customize</h4>
            <p class="card-text text-muted">Well-structured code that's easy to modify and extend.</p>
          </div>
        </div>
      </div>
    </div>
  </section>

  <!-- Videos Preview Section -->
  <section class="videos-preview-section py-5 bg-light">
    <div class="text-center mb-5">
      <h2 class="fw-bold">See Our Portal in Action</h2>
      <p class="lead text-muted">Watch demonstrations of all three user interfaces</p>
    </div>

    <div class="row g-4 mb-5">
      <div class="col-lg-4 col-md-6">
        <div class="video-preview-card bg-white rounded-4 shadow-sm overflow-hidden h-100">
          <div class="video-thumbnail position-relative">
            <img src="/images/university_logo.png" alt="Student Portal" class="w-100" style="height: 200px; object-fit: cover;">
            <div class="play-overlay position-absolute top-0 start-0 w-100 h-100 d-flex align-items-center justify-content-center bg-dark bg-opacity-50">
              <div class="play-button bg-success text-white rounded-circle d-flex align-items-center justify-content-center" style="width: 60px; height: 60px;">
                <i class="bi bi-play-fill fs-3"></i>
              </div>
            </div>
          </div>
          <div class="card-body p-4 text-center">
            <h5 class="fw-bold mb-2">Student Portal</h5>
            <p class="text-muted mb-3">Course management and academic tracking</p>
            <span class="badge bg-success rounded-pill">Student Interface</span>
          </div>
        </div>
      </div>

      <div class="col-lg-4 col-md-6">
        <div class="video-preview-card bg-white rounded-4 shadow-sm overflow-hidden h-100">
          <div class="video-thumbnail position-relative">
            <img src="/images/university_logo.png" alt="Professor Portal" class="w-100" style="height: 200px; object-fit: cover;">
            <div class="play-overlay position-absolute top-0 start-0 w-100 h-100 d-flex align-items-center justify-content-center bg-dark bg-opacity-50">
              <div class="play-button bg-info text-white rounded-circle d-flex align-items-center justify-content-center" style="width: 60px; height: 60px;">
                <i class="bi bi-play-fill fs-3"></i>
              </div>
            </div>
          </div>
          <div class="card-body p-4 text-center">
            <h5 class="fw-bold mb-2">Professor Portal</h5>
            <p class="text-muted mb-3">Course creation and student management</p>
            <span class="badge bg-info rounded-pill">Faculty Interface</span>
          </div>
        </div>
      </div>

      <div class="col-lg-4 col-md-6 mx-auto">
        <div class="video-preview-card bg-white rounded-4 shadow-sm overflow-hidden h-100">
          <div class="video-thumbnail position-relative">
            <img src="/images/university_logo.png" alt="Admin Portal" class="w-100" style="height: 200px; object-fit: cover;">
            <div class="play-overlay position-absolute top-0 start-0 w-100 h-100 d-flex align-items-center justify-content-center bg-dark bg-opacity-50">
              <div class="play-button bg-warning text-white rounded-circle d-flex align-items-center justify-content-center" style="width: 60px; height: 60px;">
                <i class="bi bi-play-fill fs-3"></i>
              </div>
            </div>
          </div>
          <div class="card-body p-4 text-center">
            <h5 class="fw-bold mb-2">Admin Portal</h5>
            <p class="text-muted mb-3">System administration and analytics</p>
            <span class="badge bg-warning rounded-pill">Administrative</span>
          </div>
        </div>
      </div>
    </div>

    <div class="text-center">
      <a href="/videos" class="btn btn-primary btn-lg rounded-pill px-5 py-3">
        <i class="bi bi-play-circle-fill me-2"></i>Watch All Videos
      </a>
    </div>
  </section>

  <!-- About & Feedback Section -->
  <section class="py-5">
    <div class="row g-4">
      <div class="col-lg-6">
        <div class="card h-100 border-0 shadow">
          <div class="card-body p-4">
            <h3 class="card-title fw-bold text-primary mb-4">
              <i class="bi bi-info-circle me-2"></i>About This Project
            </h3>
            <p class="card-text">This project demonstrates a university portal built with Express.js, EJS templates, and MongoDB. It showcases modern web development techniques and best practices.</p>
            <p class="card-text">The project includes:</p>
            <ul class="list-group list-group-flush mb-4">
              <li class="list-group-item bg-transparent px-0">
                <i class="bi bi-check-circle-fill text-success me-2"></i>User authentication system
              </li>
              <li class="list-group-item bg-transparent px-0">
                <i class="bi bi-check-circle-fill text-success me-2"></i>Feedback submission and management
              </li>
              <li class="list-group-item bg-transparent px-0">
                <i class="bi bi-check-circle-fill text-success me-2"></i>Responsive design for all devices
              </li>
              <li class="list-group-item bg-transparent px-0">
                <i class="bi bi-check-circle-fill text-success me-2"></i>Modern UI with animations
              </li>
            </ul>
            <div class="text-center mt-4">
              <a href="#download" class="btn btn-outline-primary rounded-pill px-4">
                <i class="bi bi-download me-2"></i>Get Started
              </a>
            </div>
          </div>
        </div>
      </div>

      <div class="col-lg-6">
        <div class="card h-100 border-0 shadow">
          <div class="card-body p-4">
            <h3 class="card-title fw-bold text-secondary mb-4">
              <i class="bi bi-chat-square-text me-2"></i>Submit Feedback
            </h3>
            <p class="card-text">We value your opinion! After downloading and reviewing the project, please provide your feedback to help us improve.</p>
            <p class="card-text">Your feedback helps us:</p>
            <ul class="list-group list-group-flush mb-4">
              <li class="list-group-item bg-transparent px-0">
                <i class="bi bi-star-fill text-warning me-2"></i>Improve the user experience
              </li>
              <li class="list-group-item bg-transparent px-0">
                <i class="bi bi-star-fill text-warning me-2"></i>Fix bugs and issues
              </li>
              <li class="list-group-item bg-transparent px-0">
                <i class="bi bi-star-fill text-warning me-2"></i>Add new features based on user needs
              </li>
              <li class="list-group-item bg-transparent px-0">
                <i class="bi bi-star-fill text-warning me-2"></i>Make the project more accessible
              </li>
            </ul>
            <div class="text-center mt-4">
              <a href="/feedback" class="btn btn-secondary rounded-pill px-4">
                <i class="bi bi-pencil-square me-2"></i>Submit Feedback
              </a>
            </div>
          </div>
        </div>
      </div>
    </div>
  </section>
</div>
