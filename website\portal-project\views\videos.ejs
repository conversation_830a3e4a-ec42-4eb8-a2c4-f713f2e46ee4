<!-- Project Videos Page -->
<div class="videos-container">
  <!-- Header Section -->
  <div class="videos-header text-center py-5 bg-gradient-primary text-white">
    <div class="container">
      <h1 class="display-4 fw-bold mb-3 animate__animated animate__fadeInDown">Project Demonstrations</h1>
      <p class="lead mb-4 animate__animated animate__fadeInUp animate__delay-1s">Watch our University Portal in action</p>
      <div class="d-flex justify-content-center animate__animated animate__fadeInUp animate__delay-2s">
        <div class="video-stats d-flex gap-4">
          <div class="stat-item">
            <i class="bi bi-play-circle-fill fs-2 mb-2"></i>
            <div class="fw-bold">3 Videos</div>
            <small>Demonstrations</small>
          </div>
          <div class="stat-item">
            <i class="bi bi-people-fill fs-2 mb-2"></i>
            <div class="fw-bold">3 Roles</div>
            <small>User Types</small>
          </div>
          <div class="stat-item">
            <i class="bi bi-shield-check-fill fs-2 mb-2"></i>
            <div class="fw-bold">Full</div>
            <small>Coverage</small>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Videos Section -->
  <div class="container py-5">
    <div class="row g-4">
      <!-- Student Portal Video -->
      <div class="col-lg-4 col-md-6">
        <div class="video-card bg-white rounded-4 shadow-lg overflow-hidden h-100 animate__animated animate__fadeInUp">
          <div class="video-wrapper position-relative">
            <video
              class="w-100"
              controls
              preload="metadata"
              playsinline
              style="height: 250px; object-fit: cover;">
              <source src="/images/student.mp4" type="video/mp4">
              Your browser does not support the video tag.
            </video>

          </div>
          <div class="card-body p-4">
            <div class="d-flex align-items-center mb-3">
              <div class="icon-wrapper bg-success bg-opacity-10 text-success rounded-circle p-2 me-3">
                <i class="bi bi-mortarboard-fill fs-4"></i>
              </div>
              <div>
                <h5 class="card-title mb-1 fw-bold">Student Portal</h5>
                <span class="badge bg-success rounded-pill">Student Interface</span>
              </div>
            </div>
            <p class="card-text text-muted mb-3">
              Explore the student interface featuring course enrollment, grade viewing, assignment submissions, and academic progress tracking.
            </p>
            <div class="features-list">
              <div class="feature-item d-flex align-items-center mb-2">
                <i class="bi bi-check-circle-fill text-success me-2"></i>
                <small>Course Management</small>
              </div>
              <div class="feature-item d-flex align-items-center mb-2">
                <i class="bi bi-check-circle-fill text-success me-2"></i>
                <small>Grade Tracking</small>
              </div>
              <div class="feature-item d-flex align-items-center">
                <i class="bi bi-check-circle-fill text-success me-2"></i>
                <small>Assignment Portal</small>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Professor Portal Video -->
      <div class="col-lg-4 col-md-6">
        <div class="video-card bg-white rounded-4 shadow-lg overflow-hidden h-100 animate__animated animate__fadeInUp animate__delay-1s">
          <div class="video-wrapper position-relative">
            <video
              class="w-100"
              controls
              preload="metadata"
              playsinline
              style="height: 250px; object-fit: cover;">
              <source src="/images/professor.mp4" type="video/mp4">
              Your browser does not support the video tag.
            </video>

          </div>
          <div class="card-body p-4">
            <div class="d-flex align-items-center mb-3">
              <div class="icon-wrapper bg-info bg-opacity-10 text-info rounded-circle p-2 me-3">
                <i class="bi bi-person-workspace fs-4"></i>
              </div>
              <div>
                <h5 class="card-title mb-1 fw-bold">Professor Portal</h5>
                <span class="badge bg-info rounded-pill">Faculty Interface</span>
              </div>
            </div>
            <p class="card-text text-muted mb-3">
              Discover the professor interface with course creation, student management, grading systems, and academic reporting tools.
            </p>
            <div class="features-list">
              <div class="feature-item d-flex align-items-center mb-2">
                <i class="bi bi-check-circle-fill text-info me-2"></i>
                <small>Course Creation</small>
              </div>
              <div class="feature-item d-flex align-items-center mb-2">
                <i class="bi bi-check-circle-fill text-info me-2"></i>
                <small>Student Management</small>
              </div>
              <div class="feature-item d-flex align-items-center">
                <i class="bi bi-check-circle-fill text-info me-2"></i>
                <small>Grading System</small>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Admin Portal Video -->
      <div class="col-lg-4 col-md-6 mx-auto">
        <div class="video-card bg-white rounded-4 shadow-lg overflow-hidden h-100 animate__animated animate__fadeInUp animate__delay-2s">
          <div class="video-wrapper position-relative">
            <video
              class="w-100"
              controls
              preload="metadata"
              playsinline
              style="height: 250px; object-fit: cover;">
              <source src="/images/admin.mp4" type="video/mp4">
              Your browser does not support the video tag.
            </video>

          </div>
          <div class="card-body p-4">
            <div class="d-flex align-items-center mb-3">
              <div class="icon-wrapper bg-warning bg-opacity-10 text-warning rounded-circle p-2 me-3">
                <i class="bi bi-gear-fill fs-4"></i>
              </div>
              <div>
                <h5 class="card-title mb-1 fw-bold">Admin Portal</h5>
                <span class="badge bg-warning rounded-pill">Administrative</span>
              </div>
            </div>
            <p class="card-text text-muted mb-3">
              See the administrative dashboard with user management, system configuration, analytics, and comprehensive control features.
            </p>
            <div class="features-list">
              <div class="feature-item d-flex align-items-center mb-2">
                <i class="bi bi-check-circle-fill text-warning me-2"></i>
                <small>User Management</small>
              </div>
              <div class="feature-item d-flex align-items-center mb-2">
                <i class="bi bi-check-circle-fill text-warning me-2"></i>
                <small>System Analytics</small>
              </div>
              <div class="feature-item d-flex align-items-center">
                <i class="bi bi-check-circle-fill text-warning me-2"></i>
                <small>Configuration</small>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Call to Action Section -->
    <div class="row mt-5">
      <div class="col-12">
        <div class="cta-section bg-gradient-primary text-white rounded-4 p-5 text-center">
          <h3 class="fw-bold mb-3">Ready to Try Our Portal?</h3>
          <p class="lead mb-4">Download the complete project and explore all features yourself</p>
          <div class="d-flex flex-wrap justify-content-center gap-3">
            <a href="/#download" class="btn btn-light btn-lg rounded-pill px-4 py-3">
              <i class="bi bi-download me-2"></i>Download Project
            </a>
            <a href="/feedback" class="btn btn-outline-light btn-lg rounded-pill px-4 py-3">
              <i class="bi bi-chat-square-text me-2"></i>Share Feedback
            </a>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>


